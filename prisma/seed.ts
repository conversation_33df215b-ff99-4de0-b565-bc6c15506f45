import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Iniciando seed do banco de dados...')

  // Limpar dados existentes
  await prisma.message.deleteMany()
  await prisma.conversation.deleteMany()
  await prisma.interaction.deleteMany()
  await prisma.lead.deleteMany()
  await prisma.agentMetrics.deleteMany()
  await prisma.agentAI.deleteMany()
  await prisma.campaign.deleteMany()
  await prisma.kPI.deleteMany()
  await prisma.user.deleteMany()

  // Criar usuários
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON>mini<PERSON><PERSON>',
      role: 'ADMIN',
    },
  })

  const manager = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON>',
      role: 'MANAGE<PERSON>',
    },
  })

  const user = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON>',
      role: 'USER',
    },
  })

  console.log('✅ Usuários criados')

  // Criar agentes IA
  const whatsappAgent = await prisma.agentAI.create({
    data: {
      name: 'WhatsApp Bot',
      description: 'Agente IA para atendimento via WhatsApp',
      n8nWorkflowId: 'workflow-whatsapp-001',
      status: 'ACTIVE',
      platform: 'WHATSAPP',
      configuration: JSON.stringify({
        autoResponse: true,
        businessHours: '09:00-18:00',
        language: 'pt-BR',
      }),
    },
  })

  const websiteAgent = await prisma.agentAI.create({
    data: {
      name: 'Site Chat',
      description: 'Agente IA para chat do website',
      n8nWorkflowId: 'workflow-website-001',
      status: 'ACTIVE',
      platform: 'WEBSITE',
      configuration: JSON.stringify({
        autoResponse: true,
        welcomeMessage: 'Olá! Como posso ajudar você hoje?',
        language: 'pt-BR',
      }),
    },
  })

  const instagramAgent = await prisma.agentAI.create({
    data: {
      name: 'Instagram DM',
      description: 'Agente IA para Instagram Direct Messages',
      n8nWorkflowId: 'workflow-instagram-001',
      status: 'INACTIVE',
      platform: 'INSTAGRAM',
      configuration: JSON.stringify({
        autoResponse: false,
        language: 'pt-BR',
      }),
    },
  })

  console.log('✅ Agentes IA criados')

  // Criar leads
  const leads = await Promise.all([
    prisma.lead.create({
      data: {
        name: 'João Silva',
        email: '<EMAIL>',
        phone: '+5511999999999',
        source: 'WhatsApp',
        status: 'QUALIFIED',
        score: 85,
        tags: 'interessado,urgente',
        assignedToId: user.id,
        metadata: JSON.stringify({
          company: 'Silva & Associados',
          budget: 50000,
          timeline: '30 dias',
        }),
      },
    }),
    prisma.lead.create({
      data: {
        name: 'Maria Santos',
        email: '<EMAIL>',
        phone: '+5511888888888',
        source: 'Website',
        status: 'CONTACTED',
        score: 65,
        tags: 'website,demo',
        assignedToId: manager.id,
        metadata: JSON.stringify({
          company: 'Santos Tech',
          budget: 25000,
        }),
      },
    }),
    prisma.lead.create({
      data: {
        name: 'Pedro Costa',
        email: '<EMAIL>',
        phone: '+5511777777777',
        source: 'Google Ads',
        status: 'CONVERTED',
        score: 95,
        tags: 'convertido,google-ads',
        assignedToId: user.id,
        convertedAt: new Date(),
        metadata: JSON.stringify({
          company: 'Costa Ltda',
          budget: 100000,
          product: 'CRM Premium',
        }),
      },
    }),
    prisma.lead.create({
      data: {
        name: 'Ana Oliveira',
        email: '<EMAIL>',
        phone: '+5511666666666',
        source: 'Facebook Ads',
        status: 'NEW',
        score: 45,
        tags: 'facebook,novo',
        metadata: JSON.stringify({
          interests: ['automação', 'vendas'],
        }),
      },
    }),
    prisma.lead.create({
      data: {
        name: 'Carlos Ferreira',
        email: '<EMAIL>',
        phone: '+5511555555555',
        source: 'LinkedIn',
        status: 'PROPOSAL',
        score: 78,
        tags: 'linkedin,b2b',
        assignedToId: manager.id,
        metadata: JSON.stringify({
          company: 'Ferreira Corp',
          employees: 50,
          budget: 75000,
        }),
      },
    }),
  ])

  console.log('✅ Leads criados')

  // Criar conversas
  const conversations = await Promise.all([
    prisma.conversation.create({
      data: {
        leadId: leads[0].id,
        agentId: whatsappAgent.id,
        platform: 'WHATSAPP',
        status: 'ACTIVE',
        sentimentScore: 0.8,
        duration: 1200, // 20 minutos
        messagesCount: 15,
        resolved: false,
      },
    }),
    prisma.conversation.create({
      data: {
        leadId: leads[1].id,
        agentId: websiteAgent.id,
        platform: 'WEBSITE',
        status: 'ACTIVE',
        sentimentScore: 0.6,
        duration: 800, // 13 minutos
        messagesCount: 8,
        resolved: false,
      },
    }),
    prisma.conversation.create({
      data: {
        leadId: leads[2].id,
        agentId: whatsappAgent.id,
        platform: 'WHATSAPP',
        status: 'COMPLETED',
        sentimentScore: 0.9,
        duration: 2400, // 40 minutos
        messagesCount: 32,
        resolved: true,
      },
    }),
  ])

  console.log('✅ Conversas criadas')

  // Criar mensagens de exemplo
  await Promise.all([
    prisma.message.create({
      data: {
        conversationId: conversations[0].id,
        content: 'Olá! Gostaria de saber mais sobre o CRM Falcão.',
        sender: 'LEAD',
        messageType: 'TEXT',
      },
    }),
    prisma.message.create({
      data: {
        conversationId: conversations[0].id,
        content: 'Olá João! Fico feliz em ajudar. O CRM Falcão é uma solução completa para gestão de leads e vendas. Gostaria de agendar uma demonstração?',
        sender: 'AGENT',
        messageType: 'TEXT',
      },
    }),
    prisma.message.create({
      data: {
        conversationId: conversations[0].id,
        content: 'Sim, tenho interesse! Qual seria o investimento?',
        sender: 'LEAD',
        messageType: 'TEXT',
      },
    }),
  ])

  // Criar campanhas
  await Promise.all([
    prisma.campaign.create({
      data: {
        name: 'Campanha Google Ads - CRM',
        source: 'Google Ads',
        platform: 'WEBSITE',
        cost: 5000.00,
        impressions: 50000,
        clicks: 1250,
        conversions: 45,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        isActive: true,
      },
    }),
    prisma.campaign.create({
      data: {
        name: 'Campanha Facebook - Leads',
        source: 'Facebook Ads',
        platform: 'FACEBOOK',
        cost: 3000.00,
        impressions: 75000,
        clicks: 900,
        conversions: 28,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        isActive: true,
      },
    }),
  ])

  console.log('✅ Campanhas criadas')

  // Criar KPIs dos últimos 30 dias
  const today = new Date()
  for (let i = 0; i < 30; i++) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    
    await prisma.kPI.create({
      data: {
        date,
        cpl: 45.20 + (Math.random() - 0.5) * 10,
        cac: 234.50 + (Math.random() - 0.5) * 50,
        cpc: 2.85 + (Math.random() - 0.5) * 1,
        conversionRate: 12.5 + (Math.random() - 0.5) * 5,
        roi: 285.7 + (Math.random() - 0.5) * 100,
        totalLeads: Math.floor(40 + Math.random() * 20),
        totalSales: Math.floor(5 + Math.random() * 10),
        revenue: 3000 + Math.random() * 2000,
      },
    })
  }

  console.log('✅ KPIs criados')

  // Criar métricas dos agentes
  for (let i = 0; i < 7; i++) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    
    await Promise.all([
      prisma.agentMetrics.create({
        data: {
          agentId: whatsappAgent.id,
          date,
          conversationsCount: Math.floor(15 + Math.random() * 10),
          messagesCount: Math.floor(150 + Math.random() * 100),
          avgResponseTime: 2.3 + Math.random() * 2,
          resolutionRate: 85 + Math.random() * 10,
          satisfactionScore: 4.2 + Math.random() * 0.6,
        },
      }),
      prisma.agentMetrics.create({
        data: {
          agentId: websiteAgent.id,
          date,
          conversationsCount: Math.floor(8 + Math.random() * 8),
          messagesCount: Math.floor(80 + Math.random() * 60),
          avgResponseTime: 1.8 + Math.random() * 1.5,
          resolutionRate: 78 + Math.random() * 12,
          satisfactionScore: 4.0 + Math.random() * 0.8,
        },
      }),
    ])
  }

  console.log('✅ Métricas dos agentes criadas')

  console.log('🎉 Seed concluído com sucesso!')
}

main()
  .catch((e) => {
    console.error('❌ Erro durante o seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
