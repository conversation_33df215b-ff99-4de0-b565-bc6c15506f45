// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      String   @default("USER")
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  assignedLeads Lead[]

  @@map("users")
}

model AgentAI {
  id            String   @id @default(cuid())
  name          String
  description   String?
  n8nWorkflowId String   @unique
  status        String   @default("ACTIVE")
  platform      String
  configuration String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relacionamentos
  conversations Conversation[]
  metrics       AgentMetrics[]

  @@map("agents_ai")
}

model Lead {
  id          String    @id @default(cuid())
  name        String
  email       String?
  phone       String?
  source      String
  status      String    @default("NEW")
  score       Int       @default(0)
  tags        String?
  metadata    String?
  convertedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relacionamentos
  assignedToId  String?
  assignedTo    User?          @relation(fields: [assignedToId], references: [id])
  conversations Conversation[]
  interactions  Interaction[]

  @@map("leads")
}

model Conversation {
  id             String   @id @default(cuid())
  leadId         String
  agentId        String
  platform       String
  status         String   @default("ACTIVE")
  sentimentScore Float?
  duration       Int? // em segundos
  messagesCount  Int      @default(0)
  resolved       Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relacionamentos
  lead     Lead      @relation(fields: [leadId], references: [id])
  agent    AgentAI   @relation(fields: [agentId], references: [id])
  messages Message[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(cuid())
  conversationId String
  content        String
  sender         String
  messageType    String   @default("TEXT")
  metadata       String?
  timestamp      DateTime @default(now())

  // Relacionamentos
  conversation Conversation @relation(fields: [conversationId], references: [id])

  @@map("messages")
}

model Interaction {
  id          String   @id @default(cuid())
  leadId      String
  type        String
  description String
  outcome     String?
  metadata    String?
  createdAt   DateTime @default(now())

  // Relacionamentos
  lead Lead @relation(fields: [leadId], references: [id])

  @@map("interactions")
}

model Campaign {
  id          String    @id @default(cuid())
  name        String
  source      String
  platform    String
  cost        Float     @default(0)
  impressions Int       @default(0)
  clicks      Int       @default(0)
  conversions Int       @default(0)
  startDate   DateTime
  endDate     DateTime?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("campaigns")
}

model KPI {
  id             String   @id @default(cuid())
  date           DateTime @unique
  cpl            Float // Custo por Lead
  cac            Float // Custo de Aquisição de Cliente
  cpc            Float // Custo por Clique
  conversionRate Float // Taxa de Conversão
  roi            Float // Retorno sobre Investimento
  totalLeads     Int
  totalSales     Int
  revenue        Float
  createdAt      DateTime @default(now())

  @@map("kpis")
}

model AgentMetrics {
  id                 String   @id @default(cuid())
  agentId            String
  date               DateTime
  conversationsCount Int      @default(0)
  messagesCount      Int      @default(0)
  avgResponseTime    Float    @default(0) // em segundos
  resolutionRate     Float    @default(0) // percentual
  satisfactionScore  Float    @default(0)
  createdAt          DateTime @default(now())

  // Relacionamentos
  agent AgentAI @relation(fields: [agentId], references: [id])

  @@unique([agentId, date])
  @@map("agent_metrics")
}

// Enums convertidos para strings para compatibilidade com SQLite
// UserRole: "ADMIN" | "MANAGER" | "USER"
// AgentStatus: "ACTIVE" | "INACTIVE" | "ERROR" | "MAINTENANCE"
// Platform: "WHATSAPP" | "INSTAGRAM" | "FACEBOOK" | "WEBSITE" | "EMAIL" | "TELEGRAM" | "LINKEDIN"
// LeadStatus: "NEW" | "CONTACTED" | "QUALIFIED" | "PROPOSAL" | "NEGOTIATION" | "CONVERTED" | "LOST"
// ConversationStatus: "ACTIVE" | "PAUSED" | "COMPLETED" | "ABANDONED"
// MessageSender: "AGENT" | "LEAD" | "SYSTEM"
// MessageType: "TEXT" | "IMAGE" | "AUDIO" | "VIDEO" | "DOCUMENT" | "LOCATION"
// InteractionType: "CALL" | "EMAIL" | "MEETING" | "PROPOSAL" | "FOLLOW_UP" | "NOTE"
