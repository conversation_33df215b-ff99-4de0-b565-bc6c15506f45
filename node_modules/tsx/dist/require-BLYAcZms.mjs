var m=Object.defineProperty;var a=(r,t)=>m(r,"name",{value:t,configurable:!0});import{r as o}from"./get-pipe-path-BHW2eJdv.mjs";import{r as n}from"./register-CuoYSLaL.mjs";let e;const s=a((r,t)=>(e||(e=n({namespace:Date.now().toString()})),e.require(r,t)),"tsxRequire"),i=a((r,t,c)=>(e||(e=n({namespace:Date.now().toString()})),e.resolve(r,t,c)),"resolve");i.paths=o.resolve.paths,s.resolve=i,s.main=o.main,s.extensions=o.extensions,s.cache=o.cache;export{s as t};
