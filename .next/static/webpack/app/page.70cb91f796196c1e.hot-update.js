"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/sidebar */ \"(app-pages-browser)/./src/components/layout/sidebar.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/metric-card */ \"(app-pages-browser)/./src/components/dashboard/metric-card.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst mockAgents = [\n    {\n        id: \"1\",\n        name: \"WhatsApp Bot\",\n        status: \"ACTIVE\",\n        platform: \"WHATSAPP\",\n        conversationsToday: 45,\n        avgResponseTime: 2.3,\n        lastActivity: new Date().toISOString()\n    },\n    {\n        id: \"2\",\n        name: \"Site Chat\",\n        status: \"ACTIVE\",\n        platform: \"WEBSITE\",\n        conversationsToday: 23,\n        avgResponseTime: 1.8,\n        lastActivity: new Date(Date.now() - 300000).toISOString()\n    },\n    {\n        id: \"3\",\n        name: \"Instagram DM\",\n        status: \"INACTIVE\",\n        platform: \"INSTAGRAM\",\n        conversationsToday: 0,\n        avgResponseTime: 0,\n        lastActivity: new Date(Date.now() - 3600000).toISOString()\n    }\n];\nconst mockConversations = [\n    {\n        id: \"1\",\n        leadName: \"Jo\\xe3o Silva\",\n        agentName: \"WhatsApp Bot\",\n        platform: \"WHATSAPP\",\n        status: \"ACTIVE\",\n        lastMessage: \"Quero saber mais sobre os pre\\xe7os\",\n        lastMessageTime: new Date(Date.now() - 600000).toISOString(),\n        sentimentScore: 0.8,\n        messagesCount: 12\n    },\n    {\n        id: \"2\",\n        leadName: \"Maria Santos\",\n        agentName: \"Site Chat\",\n        platform: \"WEBSITE\",\n        status: \"ACTIVE\",\n        lastMessage: \"Ainda estou pensando...\",\n        lastMessageTime: new Date(Date.now() - 900000).toISOString(),\n        sentimentScore: 0.3,\n        messagesCount: 8\n    },\n    {\n        id: \"3\",\n        leadName: \"Pedro Costa\",\n        agentName: \"WhatsApp Bot\",\n        platform: \"WHATSAPP\",\n        status: \"COMPLETED\",\n        lastMessage: \"Obrigado! Vou fechar o pedido.\",\n        lastMessageTime: new Date(Date.now() - 1800000).toISOString(),\n        sentimentScore: 0.9,\n        messagesCount: 25\n    }\n];\nfunction Dashboard() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const refreshData = async ()=>{\n        setIsLoading(true);\n        // Simular carregamento\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setIsLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden lg:ml-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                        title: \"Dashboard\",\n                        subtitle: \"Vis\\xe3o geral das m\\xe9tricas e performance dos agentes IA\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"M\\xe9tricas Principais\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Dados atualizados em tempo real\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        onClick: refreshData,\n                                        disabled: isLoading,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Atualizar\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Custo por Lead (CPL)\",\n                                        value: mockMetrics.cpl,\n                                        previousValue: mockMetrics.previousPeriod.cpl,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"M\\xe9dia dos \\xfaltimos 30 dias\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"CAC\",\n                                        value: mockMetrics.cac,\n                                        previousValue: mockMetrics.previousPeriod.cac,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Custo de Aquisi\\xe7\\xe3o de Cliente\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Taxa de Convers\\xe3o\",\n                                        value: mockMetrics.conversionRate,\n                                        previousValue: mockMetrics.previousPeriod.conversionRate,\n                                        format: \"percentage\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Leads convertidos em vendas\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"ROI\",\n                                        value: mockMetrics.roi,\n                                        previousValue: mockMetrics.previousPeriod.roi,\n                                        format: \"percentage\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Retorno sobre Investimento\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Agentes IA Ativos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: mockAgents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 rounded-lg border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"platform-icon bg-falcao-100\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getPlatformIcon)(agent.platform)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                            lineNumber: 225,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: agent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 228,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: [\n                                                                                    agent.conversationsToday,\n                                                                                    \" conversas hoje\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 229,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"status-badge \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusColor)(agent.status)),\n                                                                        children: agent.status === \"ACTIVE\" ? \"Ativo\" : \"Inativo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground mt-1\",\n                                                                        children: [\n                                                                            \"Resp: \",\n                                                                            agent.avgResponseTime,\n                                                                            \"s\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Conversas Recentes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: mockConversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"conversation-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"platform-icon bg-falcao-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getPlatformIcon)(conversation.platform)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: conversation.leadName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"status-badge \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusColor)(conversation.status)),\n                                                                                children: conversation.status === \"ACTIVE\" ? \"Ativa\" : conversation.status === \"COMPLETED\" ? \"Conclu\\xedda\" : \"Pausada\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground truncate\",\n                                                                        children: conversation.lastMessage\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatTime)(conversation.lastMessageTime),\n                                                                            \" • \",\n                                                                            conversation.messagesCount,\n                                                                            \" mensagens\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, conversation.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Total de Leads\",\n                                        value: mockMetrics.totalLeads,\n                                        previousValue: mockMetrics.previousPeriod.totalLeads,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Vendas\",\n                                        value: mockMetrics.totalSales,\n                                        previousValue: mockMetrics.previousPeriod.totalSales,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Receita\",\n                                        value: mockMetrics.revenue,\n                                        previousValue: mockMetrics.previousPeriod.revenue,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"CPC\",\n                                        value: mockMetrics.cpc,\n                                        previousValue: mockMetrics.previousPeriod.cpc,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Custo por Clique\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"EmvgwIb3cHpoFpeP+WmEDbjx4y4=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});