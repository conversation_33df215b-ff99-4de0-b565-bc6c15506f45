"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/sidebar */ \"(app-pages-browser)/./src/components/layout/sidebar.tsx\");\n/* harmony import */ var _components_layout_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/header */ \"(app-pages-browser)/./src/components/layout/header.tsx\");\n/* harmony import */ var _components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/metric-card */ \"(app-pages-browser)/./src/components/dashboard/metric-card.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,DollarSign,MessageSquare,MousePointer,RefreshCw,Target,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Funções para buscar dados das APIs\nasync function fetchMetrics() {\n    try {\n        const response = await fetch(\"/api/dashboard/metrics\");\n        const result = await response.json();\n        return result.success ? result.data : null;\n    } catch (error) {\n        console.error(\"Erro ao buscar m\\xe9tricas:\", error);\n        return null;\n    }\n}\nasync function fetchAgents() {\n    try {\n        const response = await fetch(\"/api/dashboard/agents\");\n        const result = await response.json();\n        return result.success ? result.data : [];\n    } catch (error) {\n        console.error(\"Erro ao buscar agentes:\", error);\n        return [];\n    }\n}\nasync function fetchConversations() {\n    try {\n        const response = await fetch(\"/api/dashboard/conversations\");\n        const result = await response.json();\n        return result.success ? result.data : [];\n    } catch (error) {\n        console.error(\"Erro ao buscar conversas:\", error);\n        return [];\n    }\n}\nfunction Dashboard() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const refreshData = async ()=>{\n        setIsLoading(true);\n        // Simular carregamento\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setIsLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden lg:ml-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_header__WEBPACK_IMPORTED_MODULE_3__.Header, {\n                        title: \"Dashboard\",\n                        subtitle: \"Vis\\xe3o geral das m\\xe9tricas e performance dos agentes IA\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"M\\xe9tricas Principais\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Dados atualizados em tempo real\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        onClick: refreshData,\n                                        disabled: isLoading,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Atualizar\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Custo por Lead (CPL)\",\n                                        value: mockMetrics.cpl,\n                                        previousValue: mockMetrics.previousPeriod.cpl,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"M\\xe9dia dos \\xfaltimos 30 dias\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"CAC\",\n                                        value: mockMetrics.cac,\n                                        previousValue: mockMetrics.previousPeriod.cac,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Custo de Aquisi\\xe7\\xe3o de Cliente\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Taxa de Convers\\xe3o\",\n                                        value: mockMetrics.conversionRate,\n                                        previousValue: mockMetrics.previousPeriod.conversionRate,\n                                        format: \"percentage\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Leads convertidos em vendas\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"ROI\",\n                                        value: mockMetrics.roi,\n                                        previousValue: mockMetrics.previousPeriod.roi,\n                                        format: \"percentage\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-4 h-4 text-falcao-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Retorno sobre Investimento\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Agentes IA Ativos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: mockAgents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 rounded-lg border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"platform-icon bg-falcao-100\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getPlatformIcon)(agent.platform)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: agent.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 196,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-muted-foreground\",\n                                                                                children: [\n                                                                                    agent.conversationsToday,\n                                                                                    \" conversas hoje\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 197,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 195,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"status-badge \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusColor)(agent.status)),\n                                                                        children: agent.status === \"ACTIVE\" ? \"Ativo\" : \"Inativo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground mt-1\",\n                                                                        children: [\n                                                                            \"Resp: \",\n                                                                            agent.avgResponseTime,\n                                                                            \"s\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Conversas Recentes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: mockConversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"conversation-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"platform-icon bg-falcao-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getPlatformIcon)(conversation.platform)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: conversation.leadName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 231,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"status-badge \".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.getStatusColor)(conversation.status)),\n                                                                                children: conversation.status === \"ACTIVE\" ? \"Ativa\" : conversation.status === \"COMPLETED\" ? \"Conclu\\xedda\" : \"Pausada\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground truncate\",\n                                                                        children: conversation.lastMessage\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatTime)(conversation.lastMessageTime),\n                                                                            \" • \",\n                                                                            conversation.messagesCount,\n                                                                            \" mensagens\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, conversation.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Total de Leads\",\n                                        value: mockMetrics.totalLeads,\n                                        previousValue: mockMetrics.previousPeriod.totalLeads,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Vendas\",\n                                        value: mockMetrics.totalSales,\n                                        previousValue: mockMetrics.previousPeriod.totalSales,\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"Receita\",\n                                        value: mockMetrics.revenue,\n                                        previousValue: mockMetrics.previousPeriod.revenue,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4 text-emerald-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_metric_card__WEBPACK_IMPORTED_MODULE_4__.MetricCard, {\n                                        title: \"CPC\",\n                                        value: mockMetrics.cpc,\n                                        previousValue: mockMetrics.previousPeriod.cpc,\n                                        format: \"currency\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_DollarSign_MessageSquare_MousePointer_RefreshCw_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        description: \"Custo por Clique\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/CRMFalcao/src/app/page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"EmvgwIb3cHpoFpeP+WmEDbjx4y4=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});