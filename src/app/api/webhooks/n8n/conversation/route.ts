import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { workflowId, executionId, data } = body

    // Validar dados obrigatórios
    if (!workflowId || !data) {
      return NextResponse.json(
        { success: false, error: 'workflowId e data são obrigatórios' },
        { status: 400 }
      )
    }

    // Buscar agente pelo workflowId
    const agent = await prisma.agentAI.findFirst({
      where: {
        n8nWorkflowId: workflowId,
      },
    })

    if (!agent) {
      return NextResponse.json(
        { success: false, error: 'Agente não encontrado para este workflow' },
        { status: 404 }
      )
    }

    // Buscar ou criar lead
    let lead = null
    if (data.leadId) {
      lead = await prisma.lead.findUnique({
        where: { id: data.leadId },
      })
    } else if (data.phone || data.email) {
      // Tentar encontrar lead existente por telefone ou email
      lead = await prisma.lead.findFirst({
        where: {
          OR: [
            data.phone ? { phone: data.phone } : {},
            data.email ? { email: data.email } : {},
          ].filter(Boolean),
        },
      })

      // Se não encontrar, criar novo lead
      if (!lead && (data.name || data.phone || data.email)) {
        lead = await prisma.lead.create({
          data: {
            name: data.name || 'Lead sem nome',
            email: data.email,
            phone: data.phone,
            source: agent.platform,
            metadata: data.metadata ? JSON.stringify(data.metadata) : null,
          },
        })
      }
    }

    if (!lead) {
      return NextResponse.json(
        { success: false, error: 'Não foi possível identificar ou criar o lead' },
        { status: 400 }
      )
    }

    // Buscar conversa existente ou criar nova
    let conversation = await prisma.conversation.findFirst({
      where: {
        leadId: lead.id,
        agentId: agent.id,
        status: 'ACTIVE',
      },
    })

    if (!conversation) {
      conversation = await prisma.conversation.create({
        data: {
          leadId: lead.id,
          agentId: agent.id,
          platform: agent.platform,
          status: 'ACTIVE',
        },
      })
    }

    // Se houver mensagem, criar registro da mensagem
    if (data.message) {
      await prisma.message.create({
        data: {
          conversationId: conversation.id,
          content: data.message,
          sender: data.sender || 'LEAD',
          messageType: data.messageType || 'TEXT',
          metadata: data.messageMetadata ? JSON.stringify(data.messageMetadata) : null,
        },
      })

      // Atualizar contador de mensagens na conversa
      await prisma.conversation.update({
        where: { id: conversation.id },
        data: {
          messagesCount: {
            increment: 1,
          },
          updatedAt: new Date(),
        },
      })
    }

    // Atualizar última atividade do agente
    await prisma.agentAI.update({
      where: { id: agent.id },
      data: {
        updatedAt: new Date(),
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        conversationId: conversation.id,
        leadId: lead.id,
        agentId: agent.id,
      },
    })
  } catch (error) {
    console.error('Erro no webhook de conversa:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
