import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { workflowId, data } = body

    // Validar dados obrigatórios
    if (!workflowId || !data || !data.name) {
      return NextResponse.json(
        { success: false, error: 'workflowId, data e data.name são obrigatórios' },
        { status: 400 }
      )
    }

    // Buscar agente pelo workflowId para determinar a fonte
    const agent = await prisma.agentAI.findFirst({
      where: {
        n8nWorkflowId: workflowId,
      },
    })

    const source = agent ? agent.platform : data.source || 'n8n'

    // Verificar se lead já existe
    let lead = null
    if (data.email || data.phone) {
      lead = await prisma.lead.findFirst({
        where: {
          OR: [
            data.email ? { email: data.email } : {},
            data.phone ? { phone: data.phone } : {},
          ].filter(<PERSON><PERSON>an),
        },
      })
    }

    if (lead) {
      // Atualizar lead existente
      lead = await prisma.lead.update({
        where: { id: lead.id },
        data: {
          name: data.name,
          email: data.email || lead.email,
          phone: data.phone || lead.phone,
          source: data.source || lead.source,
          status: data.status || lead.status,
          score: data.score !== undefined ? data.score : lead.score,
          tags: data.tags ? (Array.isArray(data.tags) ? data.tags.join(',') : data.tags) : lead.tags,
          metadata: data.metadata ? JSON.stringify(data.metadata) : lead.metadata,
          updatedAt: new Date(),
        },
      })
    } else {
      // Criar novo lead
      lead = await prisma.lead.create({
        data: {
          name: data.name,
          email: data.email,
          phone: data.phone,
          source,
          status: data.status || 'NEW',
          score: data.score || 0,
          tags: data.tags ? (Array.isArray(data.tags) ? data.tags.join(',') : data.tags) : null,
          metadata: data.metadata ? JSON.stringify(data.metadata) : null,
          assignedToId: data.assignedToId,
        },
      })
    }

    // Se houver dados de interação, criar registro
    if (data.interaction) {
      await prisma.interaction.create({
        data: {
          leadId: lead.id,
          type: data.interaction.type || 'NOTE',
          description: data.interaction.description || 'Interação via n8n',
          outcome: data.interaction.outcome,
          metadata: data.interaction.metadata ? JSON.stringify(data.interaction.metadata) : null,
        },
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        id: lead.id,
        name: lead.name,
        email: lead.email,
        phone: lead.phone,
        source: lead.source,
        status: lead.status,
        score: lead.score,
        tags: lead.tags ? lead.tags.split(',') : [],
        createdAt: lead.createdAt.toISOString(),
        updatedAt: lead.updatedAt.toISOString(),
      },
    })
  } catch (error) {
    console.error('Erro no webhook de lead:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
