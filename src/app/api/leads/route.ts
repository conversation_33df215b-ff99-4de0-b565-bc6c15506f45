import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '0')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const source = searchParams.get('source')
    const search = searchParams.get('search')

    // Construir filtros
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (source) {
      where.source = source
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
      ]
    }

    // Buscar leads com paginação
    const [leads, total] = await Promise.all([
      prisma.lead.findMany({
        where,
        include: {
          assignedTo: {
            select: {
              id: true,
              name: true,
            },
          },
          conversations: {
            orderBy: {
              updatedAt: 'desc',
            },
            take: 1,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: page * limit,
        take: limit,
      }),
      prisma.lead.count({ where }),
    ])

    // Transformar dados
    const leadsData = leads.map(lead => {
      const lastConversation = lead.conversations[0]

      return {
        id: lead.id,
        name: lead.name,
        email: lead.email,
        phone: lead.phone,
        source: lead.source,
        status: lead.status,
        score: lead.score,
        tags: lead.tags ? lead.tags.split(',') : [],
        lastInteraction: lastConversation?.updatedAt.toISOString() || lead.createdAt.toISOString(),
        assignedTo: lead.assignedTo?.name,
        createdAt: lead.createdAt.toISOString(),
        metadata: lead.metadata ? JSON.parse(lead.metadata) : null,
      }
    })

    return NextResponse.json({
      success: true,
      data: leadsData,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Erro ao buscar leads:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { name, email, phone, source, assignedToId, tags, metadata } = body

    // Validações básicas
    if (!name || !source) {
      return NextResponse.json(
        { success: false, error: 'Nome e fonte são obrigatórios' },
        { status: 400 }
      )
    }

    // Criar lead
    const lead = await prisma.lead.create({
      data: {
        name,
        email,
        phone,
        source,
        assignedToId,
        tags: Array.isArray(tags) ? tags.join(',') : tags,
        metadata: metadata ? JSON.stringify(metadata) : null,
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        id: lead.id,
        name: lead.name,
        email: lead.email,
        phone: lead.phone,
        source: lead.source,
        status: lead.status,
        score: lead.score,
        tags: lead.tags ? lead.tags.split(',') : [],
        assignedTo: lead.assignedTo?.name,
        createdAt: lead.createdAt.toISOString(),
        metadata: lead.metadata ? JSON.parse(lead.metadata) : null,
      },
    })
  } catch (error) {
    console.error('Erro ao criar lead:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
