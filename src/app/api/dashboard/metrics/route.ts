import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Buscar KPIs do dia atual
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    // KPIs de hoje
    const todayKPI = await prisma.kPI.findFirst({
      where: {
        date: {
          gte: today,
        },
      },
      orderBy: {
        date: 'desc',
      },
    })

    // KPIs de ontem para comparação
    const yesterdayKPI = await prisma.kPI.findFirst({
      where: {
        date: {
          gte: yesterday,
          lt: today,
        },
      },
    })

    // Se não houver dados de hoje, buscar o mais recente
    const latestKPI = todayKPI || await prisma.kPI.findFirst({
      orderBy: {
        date: 'desc',
      },
    })

    // Buscar dados agregados dos últimos 30 dias
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const last30DaysKPIs = await prisma.kPI.findMany({
      where: {
        date: {
          gte: thirtyDaysAgo,
        },
      },
      orderBy: {
        date: 'desc',
      },
    })

    // Calcular médias dos últimos 30 dias
    const avgMetrics = last30DaysKPIs.reduce(
      (acc, kpi) => ({
        cpl: acc.cpl + kpi.cpl,
        cac: acc.cac + kpi.cac,
        cpc: acc.cpc + kpi.cpc,
        conversionRate: acc.conversionRate + kpi.conversionRate,
        roi: acc.roi + kpi.roi,
        totalLeads: acc.totalLeads + kpi.totalLeads,
        totalSales: acc.totalSales + kpi.totalSales,
        revenue: acc.revenue + kpi.revenue,
      }),
      {
        cpl: 0,
        cac: 0,
        cpc: 0,
        conversionRate: 0,
        roi: 0,
        totalLeads: 0,
        totalSales: 0,
        revenue: 0,
      }
    )

    const count = last30DaysKPIs.length || 1

    const currentMetrics = {
      cpl: latestKPI?.cpl || avgMetrics.cpl / count,
      cac: latestKPI?.cac || avgMetrics.cac / count,
      cpc: latestKPI?.cpc || avgMetrics.cpc / count,
      conversionRate: latestKPI?.conversionRate || avgMetrics.conversionRate / count,
      roi: latestKPI?.roi || avgMetrics.roi / count,
      totalLeads: latestKPI?.totalLeads || Math.floor(avgMetrics.totalLeads / count),
      totalSales: latestKPI?.totalSales || Math.floor(avgMetrics.totalSales / count),
      revenue: latestKPI?.revenue || avgMetrics.revenue / count,
    }

    const previousMetrics = yesterdayKPI ? {
      cpl: yesterdayKPI.cpl,
      cac: yesterdayKPI.cac,
      cpc: yesterdayKPI.cpc,
      conversionRate: yesterdayKPI.conversionRate,
      roi: yesterdayKPI.roi,
      totalLeads: yesterdayKPI.totalLeads,
      totalSales: yesterdayKPI.totalSales,
      revenue: yesterdayKPI.revenue,
    } : null

    return NextResponse.json({
      success: true,
      data: {
        ...currentMetrics,
        previousPeriod: previousMetrics,
      },
    })
  } catch (error) {
    console.error('Erro ao buscar métricas:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
