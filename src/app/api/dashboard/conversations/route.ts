import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Buscar conversas recentes (últimas 24 horas)
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)

    const conversations = await prisma.conversation.findMany({
      where: {
        createdAt: {
          gte: yesterday,
        },
      },
      include: {
        lead: true,
        agent: true,
        messages: {
          orderBy: {
            timestamp: 'desc',
          },
          take: 1,
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: 10, // Últimas 10 conversas
    })

    // Transformar dados para o formato esperado
    const conversationsData = conversations.map(conversation => {
      const lastMessage = conversation.messages[0]

      return {
        id: conversation.id,
        leadName: conversation.lead.name,
        agentName: conversation.agent.name,
        platform: conversation.platform,
        status: conversation.status,
        lastMessage: lastMessage?.content || 'Sem mensagens',
        lastMessageTime: lastMessage?.timestamp.toISOString() || conversation.createdAt.toISOString(),
        sentimentScore: conversation.sentimentScore,
        messagesCount: conversation.messagesCount,
      }
    })

    return NextResponse.json({
      success: true,
      data: conversationsData,
    })
  } catch (error) {
    console.error('Erro ao buscar conversas:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
