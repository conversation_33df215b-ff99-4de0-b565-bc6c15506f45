import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Buscar todos os agentes IA
    const agents = await prisma.agentAI.findMany({
      include: {
        conversations: {
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)), // Hoje
            },
          },
        },
        metrics: {
          where: {
            date: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)), // Hoje
            },
          },
          orderBy: {
            date: 'desc',
          },
          take: 1,
        },
      },
    })

    // Transformar dados para o formato esperado
    const agentsData = agents.map(agent => {
      const todayMetrics = agent.metrics[0]
      const conversationsToday = agent.conversations.length

      return {
        id: agent.id,
        name: agent.name,
        status: agent.status,
        platform: agent.platform,
        conversationsToday,
        avgResponseTime: todayMetrics?.avgResponseTime || 0,
        lastActivity: agent.updatedAt.toISOString(),
        resolutionRate: todayMetrics?.resolutionRate || 0,
        satisfactionScore: todayMetrics?.satisfactionScore || 0,
      }
    })

    return NextResponse.json({
      success: true,
      data: agentsData,
    })
  } catch (error) {
    console.error('Erro ao buscar agentes:', error)
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
