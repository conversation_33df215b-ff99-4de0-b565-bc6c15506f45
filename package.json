{"name": "crm-falcao", "version": "1.0.0", "description": "CRM Falcão - Sistema CRM com Integração de Agentes IA (n8n)", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma db push --force-reset && npm run db:seed"}, "dependencies": {"@prisma/client": "^5.7.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.17.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "lucide-react": "^0.303.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "prisma": "^5.7.1", "tailwindcss": "^3.4.0", "tsx": "^4.19.4", "typescript": "^5.3.3"}}